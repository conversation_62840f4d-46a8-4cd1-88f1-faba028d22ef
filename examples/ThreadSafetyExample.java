/**
 * <PERSON><PERSON> dụ minh họa cách PlayerGUI đảm bảo thread safety khi tải bài hát
 */
public class ThreadSafetyExample {
    
    public static void demonstrateThreadSafety() {
        System.out.println("=== Demo Thread Safety trong PlayerGUI ===");
        
        // Tạo PlayerGUI instance
        PlayerScreen screen = new PlayerScreen(null);
        PlayerGUI player = screen.getPlayerGUI();
        
        // Tạo playlist với nhiều bài hát
        Track[] tracks = {
            new Track("Bài 1", "http://example.com/song1.mp3", "", "", 180),
            new Track("Bài 2", "http://example.com/song2.mp3", "", "", 200),
            new Track("Bài 3", "http://example.com/song3.mp3", "", "", 220)
        };
        
        Tracks playlist = new Tracks(tracks);
        player.setPlaylist(playlist, 0);
        
        System.out.println("1. <PERSON><PERSON>t đầu phát bài đầu tiên...");
        player.play(); // Tạo luồng tải đầu tiên
        
        // <PERSON>ô phỏng người dùng thay đổi bài hát <PERSON> chóng
        try {
            Thread.sleep(50); // Chờ một chút để luồng tải bắt đầu
            
            System.out.println("2. Chuyển sang bài tiếp theo (luồng cũ sẽ bị dừng)...");
            player.next(); // Dừng luồng cũ, tạo luồng mới
            
            Thread.sleep(50);
            
            System.out.println("3. Chuyển sang bài tiếp theo nữa (luồng cũ lại bị dừng)...");
            player.next(); // Dừng luồng cũ, tạo luồng mới
            
            Thread.sleep(50);
            
            System.out.println("4. Quay lại bài trước (luồng cũ bị dừng)...");
            player.previous(); // Dừng luồng cũ, tạo luồng mới
            
            Thread.sleep(100);
            
            System.out.println("5. Dọn dẹp (dừng tất cả luồng)...");
            player.cleanup(); // Dừng mọi luồng và dọn dẹp
            
        } catch (InterruptedException e) {
            e.printStackTrace();
        }
        
        System.out.println("=== Demo hoàn thành ===");
        System.out.println("Kết quả: Chỉ có một luồng tải được phép hoạt động tại một thời điểm");
    }
    
    /**
     * Ví dụ về tình huống trước khi cải tiến (chỉ để minh họa)
     */
    public static void demonstrateOldProblem() {
        System.out.println("=== Vấn đề trước khi cải tiến ===");
        System.out.println("Trước đây, nếu người dùng thay đổi bài hát nhanh chóng:");
        System.out.println("- Luồng 1: Đang tải bài A...");
        System.out.println("- Người dùng chuyển sang bài B");
        System.out.println("- Luồng 2: Bắt đầu tải bài B...");
        System.out.println("- Luồng 1: Vẫn đang tải bài A (không bị dừng)");
        System.out.println("- Người dùng chuyển sang bài C");
        System.out.println("- Luồng 3: Bắt đầu tải bài C...");
        System.out.println("- Kết quả: 3 luồng cùng hoạt động, gây lãng phí tài nguyên");
        System.out.println();
        
        System.out.println("=== Sau khi cải tiến ===");
        System.out.println("Bây giờ, khi người dùng thay đổi bài hát:");
        System.out.println("- Luồng 1: Đang tải bài A...");
        System.out.println("- Người dùng chuyển sang bài B");
        System.out.println("- Hệ thống: Dừng luồng 1 ngay lập tức");
        System.out.println("- Luồng 2: Bắt đầu tải bài B...");
        System.out.println("- Người dùng chuyển sang bài C");
        System.out.println("- Hệ thống: Dừng luồng 2 ngay lập tức");
        System.out.println("- Luồng 3: Bắt đầu tải bài C...");
        System.out.println("- Kết quả: Chỉ có 1 luồng hoạt động tại một thời điểm");
    }
    
    public static void main(String[] args) {
        demonstrateOldProblem();
        System.out.println();
        demonstrateThreadSafety();
    }
}
